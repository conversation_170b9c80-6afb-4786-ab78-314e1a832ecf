/**
 * App Tests
 */

import { App } from "../app";
import { RedisService } from "../services/redis.service";
import { NLUService } from "../services/nlu.service";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { createServer, Server as HttpServer } from "http";
import express, { Application } from "express";

// Mock express module directly
const mockExpressAppInstance: Partial<Application> = {
  use: jest.fn(),
  get: jest.fn(),
  listen: jest.fn(),
};

// Mock the express module itself, including its static methods
jest.mock("express", () => {
  const mockExpress = jest.fn(() => mockExpressAppInstance as Application); // Mock the default export (the express() function)

  // Attach static methods like .json() and .urlencoded() to the mocked express function
  (mockExpress as any).json = jest.fn(() => jest.fn()); // Return mock middleware
  (mockExpress as any).urlencoded = jest.fn(() => jest.fn()); // Return mock middleware

  return mockExpress;
});

// Mock createServer directly
const mockHttpServerInstance: Partial<HttpServer> = {
  listen: jest.fn((...args: any[]) => {
    const callback = args.find((arg) => typeof arg === "function");
    if (callback) callback();
    return mockHttpServerInstance as HttpServer; // Return the mocked server instance
  }) as any,
  close: jest.fn(), // Add close method for graceful shutdown tests
};
jest.mock("http", () => ({
  createServer: jest.fn(() => mockHttpServerInstance as HttpServer),
}));

// Mock routes
jest.mock("../routes/socket.routes", () => ({
  createSocketRoutes: jest.fn(),
}));
jest.mock("../routes/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue([]),
}));

describe("App", () => {
  let app: App;
  // Use the globally mocked instances directly
  const mockedExpress = express as jest.MockedFunction<typeof express>;
  const mockedCreateServer = createServer as jest.MockedFunction<typeof createServer>;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Ensure the mocked instances are clean for each test
    mockedExpress.mockClear();
    (mockedExpress.json as jest.Mock).mockClear();
    (mockedExpress.urlencoded as jest.Mock).mockClear();
    mockExpressAppInstance.use = jest.fn();
    mockExpressAppInstance.get = jest.fn();
    mockExpressAppInstance.listen = jest.fn();

    mockedCreateServer.mockClear();
    mockHttpServerInstance.listen = jest.fn((...args: any[]) => {
      const callback = args.find((arg) => typeof arg === "function");
      if (callback) callback();
      return mockHttpServerInstance as HttpServer;
    }) as any;
    mockHttpServerInstance.close = jest.fn();

    // Mock DatabaseConnection
    const mockDatabaseConnection = {
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
    };
    (DatabaseConnection as jest.Mock).mockImplementation(() => mockDatabaseConnection);

    // Mock RedisService
    const mockRedisService = {
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
    };
    (RedisService as jest.Mock).mockImplementation(() => mockRedisService);

    // Mock NLUService
    const mockNLUService = {
      testConnection: jest.fn().mockResolvedValue(true),
    };
    (NLUService as jest.Mock).mockImplementation(() => mockNLUService);

    app = new App();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("constructor", () => {
    it("should create express app and http server", () => {
      expect(mockedExpress).toHaveBeenCalledTimes(1); // express() should be called once
      expect(mockedCreateServer).toHaveBeenCalledTimes(1); // createServer() should be called once
      expect(mockedCreateServer).toHaveBeenCalledWith(mockExpressAppInstance); // Should be called with the mocked express app instance
    });
  });

  describe("start", () => {
    it("should initialize services and start server successfully", async () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      await app.start();

      expect(mockHttpServerInstance.listen).toHaveBeenCalledWith(
        expect.any(Number),
        expect.any(Function),
      );

      consoleSpy.mockRestore();
    });

    it("should handle initialization errors", async () => {
      const error = new Error("Initialization failed");
      const mockDatabaseConnection = {
        connect: jest.fn().mockRejectedValue(error),
      };
      (DatabaseConnection as jest.Mock).mockImplementation(() => mockDatabaseConnection);

      const processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
        throw new Error("process.exit called");
      });

      await expect(app.start()).rejects.toThrow("process.exit called");
      expect(processExitSpy).toHaveBeenCalledWith(1);

      processExitSpy.mockRestore();
    });
  });

  describe("stop", () => {
    it("should disconnect services gracefully", async () => {
      // First start the app to initialize services
      await app.start();

      // Mock context services for stop method
      (app as any).context = {
        redis: { disconnect: jest.fn().mockResolvedValue(undefined) },
        db: { disconnect: jest.fn().mockResolvedValue(undefined) },
        socketService: { disconnect: jest.fn().mockResolvedValue(undefined) },
      };

      await app.stop();

      expect((app as any).context.redis.disconnect).toHaveBeenCalledTimes(1);
      expect((app as any).context.db.disconnect).toHaveBeenCalledTimes(1);
      expect((app as any).context.socketService.disconnect).toHaveBeenCalledTimes(1);
    });

    it("should handle shutdown errors gracefully", async () => {
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();

      // Mock context to have services that throw errors on disconnect
      (app as any).context = {
        redis: {
          disconnect: jest.fn().mockRejectedValue(new Error("Redis disconnect failed")),
        },
        db: {
          disconnect: jest.fn().mockRejectedValue(new Error("DB disconnect failed")),
        },
        socketService: {
          disconnect: jest.fn().mockRejectedValue(new Error("Socket disconnect failed")),
        },
      };

      await app.stop();

      expect(consoleSpy).toHaveBeenCalledWith("Error during shutdown:", expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe("middleware initialization", () => {
    it("should setup security middleware", async () => {
      await app.start();

      // Verify that middleware setup methods were called
      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
    });

    it("should setup request parsing middleware", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
      expect(mockedExpress.json).toHaveBeenCalledTimes(1);
      expect(mockedExpress.urlencoded).toHaveBeenCalledTimes(1);
    });

    it("should setup logging middleware in non-test environment", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("routes initialization", () => {
    it("should setup API routes", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith("/api/v1", ...expect.any(Array));
    });

    it("should setup swagger documentation", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(
        "/api-docs",
        expect.any(Function),
        expect.any(Function),
      );
      expect(mockExpressAppInstance.get).toHaveBeenCalledWith(
        "/api-docs.json",
        expect.any(Function),
      );
    });

    it("should setup root endpoint", async () => {
      await app.start();

      expect(mockExpressAppInstance.get).toHaveBeenCalledWith("/", expect.any(Function));
    });

    it("should setup 404 handler", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith("*", expect.any(Function));
    });
  });

  describe("error handling", () => {
    it("should setup global error handler", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
    });

    it("should handle uncaught exceptions", async () => {
      const processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
        throw new Error("process.exit called");
      });

      await app.start();

      // Simulate uncaught exception
      const uncaughtExceptionHandler = process.listeners("uncaughtException").pop() as Function;
      expect(() => uncaughtExceptionHandler(new Error("Test error"))).toThrow(
        "process.exit called",
      );

      processExitSpy.mockRestore();
    });

    it("should handle unhandled promise rejections", async () => {
      const processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
        throw new Error("process.exit called");
      });

      await app.start();

      // Simulate unhandled rejection
      const unhandledRejectionHandler = process.listeners("unhandledRejection").pop() as Function;
      expect(() => unhandledRejectionHandler("Test reason", Promise.resolve())).toThrow(
        "process.exit called",
      );

      processExitSpy.mockRestore();
    });
  });

  describe("request middleware", () => {
    it("should add request ID to requests", async () => {
      await app.start();

      // Find the request ID middleware
      const middlewareCalls = (mockExpressAppInstance.use as jest.Mock).mock.calls;
      const requestIdMiddleware = middlewareCalls.find(
        (call: any) => call.length === 1 && typeof call[0] === "function",
      );

      expect(requestIdMiddleware).toBeDefined();
    });

    it("should add request logging middleware", async () => {
      await app.start();

      // Verify logging middleware was added
      const middlewareCalls = (mockExpressAppInstance.use as jest.Mock).mock.calls;
      const loggingMiddleware = middlewareCalls.find(
        (call: any) => call.length === 1 && typeof call[0] === "function",
      );

      expect(loggingMiddleware).toBeDefined();
    });
  });
});
